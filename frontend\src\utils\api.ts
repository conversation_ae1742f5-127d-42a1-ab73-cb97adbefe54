/**
 * API utility functions for the Emotion Detection System
 */

import axios, { AxiosResponse } from 'axios';

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors and backend connectivity
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle authentication errors
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      window.location.href = '/auth';
    }

    // Handle network errors (backend not available)
    if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
      console.warn('Backend server not available, falling back to demo mode');
      // Don't redirect, let components handle the fallback
    }

    return Promise.reject(error);
  }
);

// Types
export interface User {
  id: number;
  name: string;
  email: string;
  age?: number;
  gender?: string;
  notification_settings: any;
  created_at: string;
  is_active: boolean;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface EmotionResult {
  emotions: Record<string, number>;
  primary_label: string;
  confidence_score: number;
  text: string;
  detected_at: string;
}

export interface EmotionLog {
  id: number;
  user_id: number;
  date: string;
  text?: string;
  detected_emotions?: Record<string, number>;
  primary_label?: string;
  confidence_score?: number;
  notes?: string;
  is_manual: boolean;
  created_at: string;
}

export interface Activity {
  id: number;
  emotion_category: string;
  activity_description: string;
  link?: string;
  category?: string;
  duration_minutes?: number;
  difficulty_level: string;
}

export interface Challenge {
  id: number;
  user_id: number;
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  status: string;
  progress: number;
  target_days: number;
  completed_days: number;
  challenge_type: string;
  reward_points: number;
  created_at: string;
  completed_at?: string;
}

export interface DailySummary {
  date: string;
  primary_emotions: Record<string, number>;
  emotion_score: number;
  total_logs: number;
  dominant_emotion: string;
}

export interface MonthlyReport {
  month: string;
  year: number;
  daily_summaries: DailySummary[];
  emotion_distribution: Record<string, number>;
  average_score: number;
  insights: string[];
  total_logs: number;
  most_frequent_emotion: string;
  emotional_stability: string;
}

// Auth API
export const authAPI = {
  register: async (userData: {
    name: string;
    email: string;
    password: string;
    age?: number;
    gender?: string;
  }): Promise<LoginResponse> => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  login: async (credentials: {
    email: string;
    password: string;
  }): Promise<LoginResponse> => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  refreshToken: async (): Promise<LoginResponse> => {
    const response = await api.post('/auth/refresh');
    return response.data;
  },
};

// Emotion API
export const emotionAPI = {
  detectEmotion: async (text: string): Promise<EmotionResult> => {
    const response = await api.post('/emotion/detect', { text });
    return response.data;
  },

  getHistory: async (limit = 50, offset = 0): Promise<EmotionLog[]> => {
    const response = await api.get(`/emotion/history?limit=${limit}&offset=${offset}`);
    return response.data;
  },

  getRecent: async (): Promise<EmotionLog> => {
    const response = await api.get('/emotion/recent');
    return response.data;
  },

  getStats: async () => {
    const response = await api.get('/emotion/stats');
    return response.data;
  },

  deleteLog: async (logId: number): Promise<void> => {
    await api.delete(`/emotion/history/${logId}`);
  },
};

// Diary API
export const diaryAPI = {
  createLog: async (data: {
    emotion: string;
    notes?: string;
    intensity?: number;
  }): Promise<EmotionLog> => {
    const response = await api.post('/diary/log', data);
    return response.data;
  },

  getEntries: async (params?: {
    start_date?: string;
    end_date?: string;
    limit?: number;
    offset?: number;
  }): Promise<EmotionLog[]> => {
    const queryParams = new URLSearchParams();
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());

    const response = await api.get(`/diary/entries?${queryParams}`);
    return response.data;
  },

  getEntry: async (entryId: number): Promise<EmotionLog> => {
    const response = await api.get(`/diary/entries/${entryId}`);
    return response.data;
  },

  updateEntry: async (entryId: number, notes: string): Promise<EmotionLog> => {
    const response = await api.put(`/diary/entries/${entryId}`, { notes });
    return response.data;
  },

  deleteEntry: async (entryId: number): Promise<void> => {
    await api.delete(`/diary/entries/${entryId}`);
  },

  getTodayEntries: async (): Promise<EmotionLog[]> => {
    const response = await api.get('/diary/today');
    return response.data;
  },

  getPrompts: async (): Promise<{ prompts: string[] }> => {
    const response = await api.get('/diary/prompts');
    return response.data;
  },
};

// Activities API
export const activitiesAPI = {
  getSuggestions: async (emotion?: string): Promise<{
    activities: Activity[];
    based_on_emotion: string;
    suggestion_reason: string;
  }> => {
    const params = emotion ? `?emotion=${emotion}` : '';
    const response = await api.get(`/activities/suggestions${params}`);
    return response.data;
  },

  getAll: async (): Promise<Activity[]> => {
    const response = await api.get('/activities/all');
    return response.data;
  },

  getByCategory: async (category: string): Promise<Activity[]> => {
    const response = await api.get(`/activities/by-category/${category}`);
    return response.data;
  },

  getPersonalized: async (): Promise<Activity[]> => {
    const response = await api.get('/activities/personalized');
    return response.data;
  },
};

// Challenges API
export const challengesAPI = {
  create: async (data: {
    title: string;
    description: string;
    target_days: number;
    challenge_type: string;
  }): Promise<Challenge> => {
    const response = await api.post('/challenges/create', data);
    return response.data;
  },

  getActive: async (): Promise<Challenge[]> => {
    const response = await api.get('/challenges/active');
    return response.data;
  },

  getAll: async (): Promise<Challenge[]> => {
    const response = await api.get('/challenges/all');
    return response.data;
  },

  update: async (challengeId: number, data: {
    progress?: number;
    completed_days?: number;
    status?: string;
  }): Promise<Challenge> => {
    const response = await api.put(`/challenges/${challengeId}`, data);
    return response.data;
  },

  delete: async (challengeId: number): Promise<void> => {
    await api.delete(`/challenges/${challengeId}`);
  },

  getTemplates: async (): Promise<{ templates: any[] }> => {
    const response = await api.get('/challenges/templates');
    return response.data;
  },

  acceptTemplate: async (templateIndex: number): Promise<Challenge> => {
    const response = await api.post('/challenges/accept-template', { template_index: templateIndex });
    return response.data;
  },

  getBadges: async (): Promise<any[]> => {
    const response = await api.get('/challenges/badges');
    return response.data;
  },
};

// Reports API
export const reportsAPI = {
  getDailySummary: async (date?: string): Promise<DailySummary> => {
    const params = date ? `?target_date=${date}` : '';
    const response = await api.get(`/reports/daily-summary${params}`);
    return response.data;
  },

  getWeeklyTrend: async (): Promise<{ weekly_trend: DailySummary[] }> => {
    const response = await api.get('/reports/weekly-trend');
    return response.data;
  },

  getMonthlyReport: async (month?: number, year?: number): Promise<MonthlyReport> => {
    const params = new URLSearchParams();
    if (month) params.append('month', month.toString());
    if (year) params.append('year', year.toString());

    const response = await api.get(`/reports/monthly-report?${params}`);
    return response.data;
  },
};

// Notifications API
export const notificationsAPI = {
  getSettings: async () => {
    const response = await api.get('/notifications/settings');
    return response.data;
  },

  updateSettings: async (settings: any) => {
    const response = await api.put('/notifications/settings', settings);
    return response.data;
  },

  getHistory: async (limit = 50, offset = 0) => {
    const response = await api.get(`/notifications/history?limit=${limit}&offset=${offset}`);
    return response.data;
  },

  markAsRead: async (notificationId: number): Promise<void> => {
    await api.put(`/notifications/${notificationId}/read`);
  },

  getUnreadCount: async (): Promise<{ unread_count: number }> => {
    const response = await api.get('/notifications/unread-count');
    return response.data;
  },

  clearAll: async (): Promise<void> => {
    await api.delete('/notifications/clear-all');
  },
};

// Mock data for demo mode when backend isn't available
export const mockData = {
  dailySummary: {
    emotion_score: 75,
    dominant_emotion: 'happy',
    total_logs: 5,
    date: new Date().toISOString().split('T')[0]
  },

  activities: [
    {
      id: 1,
      emotion_category: 'positive',
      activity_description: 'Take a nature walk and appreciate your surroundings',
      link: null,
      category: 'outdoor',
      duration_minutes: 20,
      difficulty_level: 'easy'
    },
    {
      id: 2,
      emotion_category: 'positive',
      activity_description: 'Practice gratitude journaling',
      link: null,
      category: 'mindfulness',
      duration_minutes: 10,
      difficulty_level: 'easy'
    },
    {
      id: 3,
      emotion_category: 'general',
      activity_description: 'Listen to calming music',
      link: null,
      category: 'relaxation',
      duration_minutes: 15,
      difficulty_level: 'easy'
    }
  ],

  challenges: [
    {
      id: 1,
      title: '7-Day Mindfulness Challenge',
      description: 'Practice mindfulness meditation for 10 minutes daily',
      duration_days: 7,
      difficulty_level: 'easy',
      points_reward: 100,
      is_active: true
    },
    {
      id: 2,
      title: 'Gratitude Week',
      description: 'Write down 3 things you\'re grateful for each day',
      duration_days: 7,
      difficulty_level: 'easy',
      points_reward: 75,
      is_active: true
    }
  ],

  weeklyTrend: [
    { date: '2025-07-14', emotion_score: 70, dominant_emotion: 'neutral', total_logs: 3 },
    { date: '2025-07-15', emotion_score: 65, dominant_emotion: 'sad', total_logs: 4 },
    { date: '2025-07-16', emotion_score: 80, dominant_emotion: 'happy', total_logs: 6 },
    { date: '2025-07-17', emotion_score: 75, dominant_emotion: 'happy', total_logs: 5 },
    { date: '2025-07-18', emotion_score: 85, dominant_emotion: 'joy', total_logs: 7 },
    { date: '2025-07-19', emotion_score: 72, dominant_emotion: 'content', total_logs: 4 },
    { date: '2025-07-20', emotion_score: 75, dominant_emotion: 'happy', total_logs: 5 }
  ]
};

// Helper function to check if we're in demo mode
export const isDemoMode = () => {
  const token = localStorage.getItem('access_token');
  return token && token.startsWith('demo-token-');
};

export default api;
