{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/app/emotion-input/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { emotionAPI, activitiesAPI } from '@/utils/api';\nimport type { EmotionResult, Activity } from '@/utils/api';\n\nexport default function EmotionInputPage() {\n  const { isAuthenticated } = useAuth();\n  const router = useRouter();\n  const [text, setText] = useState('');\n  const [result, setResult] = useState<EmotionResult | null>(null);\n  const [activities, setActivities] = useState<Activity[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/auth');\n    }\n  }, [isAuthenticated, router]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!text.trim()) {\n      setError('Please enter some text to analyze');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      // Detect emotion\n      const emotionResult = await emotionAPI.detectEmotion(text.trim());\n      setResult(emotionResult);\n\n      // Get activity suggestions based on detected emotion\n      const activitySuggestions = await activitiesAPI.getSuggestions(emotionResult.primary_label);\n      setActivities(activitySuggestions.activities.slice(0, 5));\n\n    } catch (err: any) {\n      setError(err.response?.data?.detail || 'Failed to analyze emotion');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleClear = () => {\n    setText('');\n    setResult(null);\n    setActivities([]);\n    setError('');\n  };\n\n  const getEmotionColor = (label: string) => {\n    const colors: Record<string, string> = {\n      positive: 'text-green-600 bg-green-100 border-green-200',\n      negative: 'text-red-600 bg-red-100 border-red-200',\n      neutral: 'text-gray-600 bg-gray-100 border-gray-200',\n    };\n    return colors[label] || 'text-gray-600 bg-gray-100 border-gray-200';\n  };\n\n  const getTopEmotions = (emotions: Record<string, number>) => {\n    return Object.entries(emotions)\n      .sort(([, a], [, b]) => b - a)\n      .slice(0, 5);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Emotion Detection</h1>\n              <p className=\"text-gray-600\">Analyze your emotions from text</p>\n            </div>\n            <button\n              onClick={() => router.push('/dashboard')}\n              className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n            >\n              ← Back to Dashboard\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Input Form */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <form onSubmit={handleSubmit}>\n                <div className=\"mb-4\">\n                  <label htmlFor=\"emotion-text\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    How are you feeling? Describe your emotions in text:\n                  </label>\n                  <textarea\n                    id=\"emotion-text\"\n                    value={text}\n                    onChange={(e) => setText(e.target.value)}\n                    rows={4}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"e.g., I'm feeling really happy today because I accomplished my goals...\"\n                    disabled={isLoading}\n                  />\n                </div>\n\n                {error && (\n                  <div className=\"mb-4 rounded-md bg-red-50 p-4\">\n                    <div className=\"text-sm text-red-700\">{error}</div>\n                  </div>\n                )}\n\n                <div className=\"flex space-x-3\">\n                  <button\n                    type=\"submit\"\n                    disabled={isLoading || !text.trim()}\n                    className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium disabled:cursor-not-allowed\"\n                  >\n                    {isLoading ? (\n                      <div className=\"flex items-center\">\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                        Analyzing...\n                      </div>\n                    ) : (\n                      'Analyze Emotion'\n                    )}\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={handleClear}\n                    className=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Clear\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n\n          {/* Results */}\n          {result && (\n            <div className=\"space-y-6\">\n              {/* Primary Emotion */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Primary Emotion</h3>\n                  <div className=\"flex items-center justify-between\">\n                    <div className={`inline-flex px-4 py-2 rounded-lg border ${getEmotionColor(result.primary_label)}`}>\n                      <span className=\"text-lg font-semibold capitalize\">{result.primary_label}</span>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-2xl font-bold text-gray-900\">\n                        {Math.round(result.confidence_score * 100)}%\n                      </div>\n                      <div className=\"text-sm text-gray-500\">Confidence</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Detailed Emotions */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Detailed Emotion Analysis</h3>\n                  <div className=\"space-y-3\">\n                    {getTopEmotions(result.emotions).map(([emotion, score]) => (\n                      <div key={emotion} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <span className=\"text-sm font-medium text-gray-900 capitalize w-24\">\n                            {emotion}\n                          </span>\n                          <div className=\"flex-1 mx-4\">\n                            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                              <div\n                                className=\"bg-blue-600 h-2 rounded-full\"\n                                style={{ width: `${score * 100}%` }}\n                              ></div>\n                            </div>\n                          </div>\n                        </div>\n                        <span className=\"text-sm text-gray-600 w-12 text-right\">\n                          {Math.round(score * 100)}%\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Activity Suggestions */}\n              {activities.length > 0 && (\n                <div className=\"bg-white shadow rounded-lg\">\n                  <div className=\"px-4 py-5 sm:p-6\">\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                      Suggested Activities for {result.primary_label} emotions\n                    </h3>\n                    <div className=\"space-y-4\">\n                      {activities.map((activity) => (\n                        <div key={activity.id} className=\"border-l-4 border-blue-400 pl-4 py-2\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {activity.activity_description}\n                          </div>\n                          <div className=\"text-xs text-gray-500 mt-1\">\n                            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2\">\n                              {activity.category}\n                            </span>\n                            {activity.duration_minutes && (\n                              <span className=\"mr-2\">{activity.duration_minutes} min</span>\n                            )}\n                            <span className=\"capitalize\">{activity.difficulty_level}</span>\n                          </div>\n                          {activity.link && (\n                            <a\n                              href={activity.link}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"text-blue-600 hover:text-blue-800 text-xs mt-1 inline-block\"\n                            >\n                              Learn more →\n                            </a>\n                          )}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Action Buttons */}\n              <div className=\"flex space-x-4\">\n                <button\n                  onClick={() => router.push('/diary')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  Add to Diary\n                </button>\n                <button\n                  onClick={() => router.push('/dashboard')}\n                  className=\"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  Back to Dashboard\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;YACd;QACF;qCAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,iBAAiB;YACjB,MAAM,gBAAgB,MAAM,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,KAAK,IAAI;YAC9D,UAAU;YAEV,qDAAqD;YACrD,MAAM,sBAAsB,MAAM,sHAAA,CAAA,gBAAa,CAAC,cAAc,CAAC,cAAc,aAAa;YAC1F,cAAc,oBAAoB,UAAU,CAAC,KAAK,CAAC,GAAG;QAExD,EAAE,OAAO,KAAU;gBACR,oBAAA;YAAT,SAAS,EAAA,gBAAA,IAAI,QAAQ,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,MAAM,KAAI;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,UAAU;QACV,cAAc,EAAE;QAChB,SAAS;IACX;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAiC;YACrC,UAAU;YACV,UAAU;YACV,SAAS;QACX;QACA,OAAO,MAAM,CAAC,MAAM,IAAI;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,OAAO,OAAO,CAAC,UACnB,IAAI,CAAC;gBAAC,GAAG,EAAE,UAAE,GAAG,EAAE;mBAAK,IAAI;WAC3B,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,UAAU;;sDACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAe,WAAU;8DAA+C;;;;;;8DAGvF,6LAAC;oDACC,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACvC,MAAM;oDACN,WAAU;oDACV,aAAY;oDACZ,UAAU;;;;;;;;;;;;wCAIb,uBACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;sDAI3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,UAAU,aAAa,CAAC,KAAK,IAAI;oDACjC,WAAU;8DAET,0BACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;4DAAuE;;;;;;+DAIxF;;;;;;8DAGJ,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASR,wBACC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,2CAAgF,OAAtC,gBAAgB,OAAO,aAAa;kEAC7F,cAAA,6LAAC;4DAAK,WAAU;sEAAoC,OAAO,aAAa;;;;;;;;;;;kEAE1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,KAAK,KAAK,CAAC,OAAO,gBAAgB,GAAG;oEAAK;;;;;;;0EAE7C,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO/C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;0DACZ,eAAe,OAAO,QAAQ,EAAE,GAAG,CAAC;wDAAC,CAAC,SAAS,MAAM;yEACpD,6LAAC;wDAAkB,WAAU;;0EAC3B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFACb;;;;;;kFAEH,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFACC,WAAU;gFACV,OAAO;oFAAE,OAAO,AAAC,GAAc,OAAZ,QAAQ,KAAI;gFAAG;;;;;;;;;;;;;;;;;;;;;;0EAK1C,6LAAC;gEAAK,WAAU;;oEACb,KAAK,KAAK,CAAC,QAAQ;oEAAK;;;;;;;;uDAfnB;;;;;;;;;;;;;;;;;;;;;;gCAwBjB,WAAW,MAAM,GAAG,mBACnB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAyC;oDAC3B,OAAO,aAAa;oDAAC;;;;;;;0DAEjD,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAI,WAAU;0EACZ,SAAS,oBAAoB;;;;;;0EAEhC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFACb,SAAS,QAAQ;;;;;;oEAEnB,SAAS,gBAAgB,kBACxB,6LAAC;wEAAK,WAAU;;4EAAQ,SAAS,gBAAgB;4EAAC;;;;;;;kFAEpD,6LAAC;wEAAK,WAAU;kFAAc,SAAS,gBAAgB;;;;;;;;;;;;4DAExD,SAAS,IAAI,kBACZ,6LAAC;gEACC,MAAM,SAAS,IAAI;gEACnB,QAAO;gEACP,KAAI;gEACJ,WAAU;0EACX;;;;;;;uDAnBK,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;8CA+B/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAxPwB;;QACM,kIAAA,CAAA,UAAO;QACpB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}